<template>
  <div class="realtime-monitor-container">
    <div class="monitor-header" v-if="title && title.length > 0">
      <h3 class="monitor-title">{{ title }}</h3>
    </div>

    <div class="monitor-content">
      <!-- 统计信息 -->
      <div class="statistics">
        <div class="stat-item">
          <span class="stat-value">124</span>
          <span class="stat-label one">Online</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">12</span>
          <span class="stat-label two">Offline</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">18</span>
          <span class="stat-label three">Failure</span>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <!-- <div v-if="error" class="error-message">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
      <el-button @click="reconnect" size="small" type="primary">重连</el-button>
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { mockWebSocket } from "@/utils/mockData";
import { ElMessage } from "element-plus";
import {
  Connection,
  Warning,
  Loading,
  ArrowUp,
  ArrowDown,
  Minus,
} from "@element-plus/icons-vue";

const props = defineProps({
  title: {
    type: String,
    default: "设备状态监测",
  },
  subTitle: {
    type: String,
    default: "",
  },
  iconName: {
    type: String,
    default: "",
  },
});

// DeviceStatusMonitor 组件只负责显示设备状态，不需要数据处理逻辑
</script>

<style scoped>
.realtime-monitor-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom:10px;
  /* background: rgba(0, 162, 255, 0.1); */
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.monitor-title {
  color: #00a2ff;
  font-size: 24px;
  font-weight: 600;
  margin: 10px auto;
}
.sub-title {
  color: #00a2ff;
  font-size: 20px;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  color: #ffffff;
}

.monitor-content {
  flex: 1;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
}

.data-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.main-value {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  position: relative;
}
.main-value .icon {
  font-size: 48px;
  color: #fff;
  /* width: 60px;
  height: 60px;
  background: url("@/assets/icon-temp.svg") no-repeat center center;
  background-size: contain;
  margin-right: 20px; */
}

.value {
  font-size: 38px;
  font-weight: bold;
  color: #00a2ff;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.unit {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.8;
}

.trend-indicator {
  position: absolute;
  top: -8px;
  right: -20px;
}

.trend-icon {
  font-size: 16px;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-badge.danger {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.last-update {
  font-size: 10px;
  color: #ffffff;
  opacity: 0.6;
}

.statistics {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  align-items: center;
  justify-content: flex-end;
  padding: 10px;
  border-radius: 8px;
  width: 95px;
  height: 95px;
}
.stat-label.one {
  background-image: url("@/assets/device-status1.png");
}
.stat-label.two {
  background-image: url("@/assets/device-status2.png");
}
.stat-label.three {
  background-image: url("@/assets/device-status3.png");
}
.stat-value {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  font-size: 22px;
  color: #00a2ff;
  font-weight: 600;
  margin-bottom: 25px;
}

.error-message {
  padding: 12px 16px;
  background: rgba(255, 107, 107, 0.1);
  border-top: 1px solid rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  font-size: 12px;
}

.error-message .el-icon {
  font-size: 14px;
}

.error-message .el-button {
  margin-left: auto;
}
</style>
