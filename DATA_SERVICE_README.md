# 青马大桥除湿监测系统 - 数据服务配置指南

## 概述

本系统已完成数据服务的重构，支持通过环境配置在 Mock 数据和真实 API 数据之间切换。

## 功能特性

### 1. 环境配置切换
- ✅ 支持开发环境和生产环境配置
- ✅ 通过 `.env` 文件控制数据源
- ✅ Mock 数据和真实 API 无缝切换

### 2. HTTP 请求封装
- ✅ 基于 Axios 的统一请求封装
- ✅ 请求/响应拦截器
- ✅ 错误处理和重试机制
- ✅ Token 自动管理

### 3. WebSocket 通信
- ✅ 基于 SockJS 的 WebSocket 封装
- ✅ 自动重连机制
- ✅ 心跳检测
- ✅ 事件管理系统

### 4. API 接口管理
- ✅ 模块化的 API 接口定义
- ✅ 监测数据接口
- ✅ 设备管理接口
- ✅ 用户管理接口
- ✅ 系统管理接口

## 文件结构

```
src/
├── api/                    # API 接口定义
│   ├── index.js           # 统一导出
│   ├── monitor.js         # 监测数据接口
│   ├── device.js          # 设备管理接口
│   ├── user.js            # 用户管理接口
│   └── system.js          # 系统管理接口
├── utils/
│   ├── request.js         # Axios 封装
│   ├── websocket.js       # WebSocket 封装
│   ├── dataService.js     # 数据服务管理器
│   ├── config.js          # 配置管理
│   └── mockData.js        # Mock 数据（保留）
├── .env                   # 开发环境配置
└── .env.production        # 生产环境配置
```

## 环境配置

### 开发环境 (.env)
```bash
# 数据源配置 - mock: 使用模拟数据, api: 使用真实API
VITE_DATA_SOURCE=mock

# API 基础地址
VITE_API_BASE_URL=http://localhost:8080/api/v1

# WebSocket 地址
VITE_WS_URL=ws://localhost:8080/ws

# 是否启用调试模式
VITE_DEBUG=true
```

### 生产环境 (.env.production)
```bash
# 数据源配置
VITE_DATA_SOURCE=api

# API 基础地址
VITE_API_BASE_URL=https://api.qingma-bridge.com/api/v1

# WebSocket 地址
VITE_WS_URL=wss://api.qingma-bridge.com/ws

# 是否启用调试模式
VITE_DEBUG=false
```

## 使用方法

### 1. 切换数据源

#### 使用 Mock 数据（开发/测试）
```bash
# 修改 .env 文件
VITE_DATA_SOURCE=mock
```

#### 使用真实 API（生产）
```bash
# 修改 .env 文件
VITE_DATA_SOURCE=api
```

### 2. HTTP 请求示例

```javascript
import { getHistoryData, getRealTimeSnapshot } from '@/utils/dataService'

// 获取历史数据
const historyData = await getHistoryData('air', 'humidity', 24)

// 获取实时数据
const realtimeData = await getRealTimeSnapshot()
```

### 3. WebSocket 使用示例

```javascript
import { 
  connectWebSocket, 
  onWebSocketEvent, 
  offWebSocketEvent 
} from '@/utils/dataService'

// 连接 WebSocket
await connectWebSocket()

// 监听实时数据
const handleRealTimeData = (data) => {
  console.log('实时数据:', data)
}

onWebSocketEvent('realTimeData', handleRealTimeData)

// 移除监听器
offWebSocketEvent('realTimeData', handleRealTimeData)
```

### 4. 直接使用 API 接口

```javascript
import * as api from '@/api'

// 获取设备状态
const deviceStatus = await api.getDeviceStatus()

// 用户登录
const loginResult = await api.login('admin', '123456')

// 获取监测点位
const points = await api.getMonitorPoints()
```

## API 接口说明

### 监测数据接口 (monitor.js)
- `getHistoryData()` - 获取历史监测数据
- `getRealTimeSnapshot()` - 获取实时数据快照
- `getPointData()` - 获取指定点位数据
- `getMonitorPoints()` - 获取监测点位列表
- `getDataStatistics()` - 获取数据统计信息
- `getThresholds()` - 获取阈值配置

### 设备管理接口 (device.js)
- `getDeviceStatus()` - 获取设备状态
- `getDeviceList()` - 获取设备列表
- `controlDevice()` - 设备控制
- `getDeviceHistory()` - 获取设备历史状态

### 用户管理接口 (user.js)
- `login()` - 用户登录
- `logout()` - 用户登出
- `getUserInfo()` - 获取用户信息
- `changePassword()` - 修改密码

### 系统管理接口 (system.js)
- `getSystemInfo()` - 获取系统信息
- `getSystemConfig()` - 获取系统配置
- `getSystemLogs()` - 获取系统日志
- `uploadFile()` - 文件上传

## 配置管理

### 使用配置工具
```javascript
import { 
  isMockMode, 
  isDebugMode, 
  getMonitorPoint,
  getThresholdConfig 
} from '@/utils/config'

// 检查当前数据源
if (isMockMode()) {
  console.log('当前使用 Mock 数据')
}

// 获取监测点位信息
const point = getMonitorPoint('1')

// 获取阈值配置
const thresholds = getThresholdConfig('humidity')
```

## 调试和监控

### 开启调试模式
```bash
# .env 文件中设置
VITE_DEBUG=true
```

调试模式下会在控制台输出：
- 🚀 HTTP 请求信息
- ✅ HTTP 响应信息
- 📡 WebSocket 连接状态
- 🔧 应用配置信息

### 查看配置信息
打开浏览器控制台，可以看到应用启动时的配置信息输出。

## 部署说明

### 开发环境部署
```bash
npm run dev
```
自动使用 `.env` 配置文件，默认使用 Mock 数据。

### 生产环境部署
```bash
npm run build
```
自动使用 `.env.production` 配置文件，默认使用真实 API。

## 注意事项

1. **环境变量前缀**：所有自定义环境变量必须以 `VITE_` 开头
2. **配置文件优先级**：`.env.production` > `.env`
3. **Mock 数据保留**：原有的 Mock 数据系统保留，便于开发和测试
4. **向后兼容**：现有组件无需大幅修改，只需更新导入路径

## 故障排除

### 1. 数据源切换不生效
- 检查环境变量是否正确设置
- 重启开发服务器
- 清除浏览器缓存

### 2. API 请求失败
- 检查 API 地址配置
- 确认后端服务是否启动
- 查看网络请求日志

### 3. WebSocket 连接失败
- 检查 WebSocket 地址配置
- 确认防火墙设置
- 查看浏览器控制台错误信息

## 后续扩展

1. **添加新的 API 接口**：在对应的 API 文件中添加新方法
2. **扩展 Mock 数据**：在 `mockData.js` 中添加新的数据生成器
3. **自定义请求拦截器**：在 `request.js` 中添加业务逻辑
4. **WebSocket 事件扩展**：在 `websocket.js` 中添加新的事件类型
