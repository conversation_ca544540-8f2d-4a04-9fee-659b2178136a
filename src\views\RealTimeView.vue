<template>
  <div class="realtime-view-container">
    <!-- 实时监测面板 -->
    <div class="realtime-content">
      <!-- 单个监测器显示 -->
      <div v-if="!showOverview" class="single-monitor">

        <RealTimeMonitor
          :title="title"
          :id="id"
          :thresholds="getThresholds(getCurrentMonitor()?.primaryDataType || 'humidity')"
        />
      </div>

      <!-- 总览显示 -->
      <div v-else class="overview-monitors">
        <div class="monitor-grid">
          <div
            v-for="monitor in filteredMonitors"
            :key="monitor.id"
            class="monitor-panel"
            @click="navigateToMonitor(monitor.id)"
          >
            <RealTimeMonitor
              :title="monitor.title"
              :id="monitor.id"
              :thresholds="getThresholds(monitor.primaryDataType)"
            />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import RealTimeMonitor from '@/components/RealTimeMonitorBox.vue'


const route = useRoute()
const router = useRouter()

// 路由参数
const id = computed(() => route.params.id || '')
const showOverview = computed(() => !id.value)
const title = computed(() => {
  if(id.value){
    return allMonitors.value.find(monitor => monitor.id === id.value)?.title || ''
  }
  return '实时监测总览'
})

// 获取当前监测器配置
const getCurrentMonitor = () => {
  if (!id.value) return null
  return allMonitors.value.find(monitor => monitor.id === id.value)
}

// 阈值配置
const thresholds = ref({
  humidity: {
    warning: { min: 30, max: 80 },
    danger: { min: 20, max: 90 }
  },
  pressure: {
    warning: { min: 1005, max: 1020 },
    danger: { min: 1000, max: 1025 }
  },
  temperature: {
    warning: { min: 18, max: 28 },
    danger: { min: 15, max: 32 }
  },
  flow: {
    warning: { min: 100, max: 150 },
    danger: { min: 80, max: 180 }
  }
})

// 所有监测器配置 - 四个点位，每个点位包含四个数据项
const allMonitors = ref([
  {
    id: '1',
    title: 'Exhaust Sleeve（No. 49）',
    primaryDataType: 'humidity', // 主要显示的数据类型
    dataItems: ['humidity', 'temperature', 'flow', 'pressure']
  },
  {
    id: '2',
    title: 'Injection Sleeve（No. 57）',
    primaryDataType: 'temperature',
    dataItems: ['humidity', 'temperature', 'flow', 'pressure']
  },
  {
    id: '3',
    title: 'Plant Room（No. 61）',
    primaryDataType: 'flow',
    dataItems: ['humidity', 'temperature', 'flow', 'pressure']
  },
  {
    id: '4',
    title: 'Exhaust Sleeve（No. 66）',
    primaryDataType: 'pressure',
    dataItems: ['humidity', 'temperature', 'flow', 'pressure']
  }
])

// 计算属性
const filteredMonitors = computed(() => {
  return allMonitors.value
})

// 方法
const getThresholds = (dataType) => {
  return thresholds.value[dataType] || { warning: { min: 0, max: 100 }, danger: { min: 0, max: 100 } }
}

const getDataTypeLabel = (dataType) => {
  const labels = {
    humidity: '湿度监测',
    pressure: '压力监测',
    temperature: '温度监测',
    flow: '流量监测'
  }
  return labels[dataType] || dataType
}

// 导航方法
const navigateToMonitor = (id) => {
  router.push(`/realtime/${id}`)
}
</script>

<style scoped>
.realtime-view-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  overflow: hidden;
}

.realtime-content {
  width: 100%;
  height: 100vh;
  /* padding: 10px; */
  padding: 0px;
  overflow: hidden;
}

.single-monitor {
  width: 100%;
  height: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.overview-monitors {
  width: 100%;
  height: 100%;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  height: 100%;
}

.monitor-panel {
  min-height: 280px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.monitor-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 162, 255, 0.2);
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .monitor-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .monitor-grid {
    grid-template-columns: 1fr;
  }

  .realtime-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-controls {
    order: -1;
    justify-content: center;
  }

  .single-monitor {
    max-width: 100%;
  }
}
</style>
