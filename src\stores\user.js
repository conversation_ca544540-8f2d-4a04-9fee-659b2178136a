import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('token') || '')

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 登录方法
  const login = async (username, password) => {
    try {
      // 模拟登录验证
      if (username === 'admin' && password === '123456') {
        const mockToken = 'mock-jwt-token-' + Date.now()
        const mockUserInfo = {
          id: 1,
          username: username,
          name: '管理员',
          role: 'admin'
        }
        
        token.value = mockToken
        userInfo.value = mockUserInfo
        
        // 保存到本地存储
        localStorage.setItem('token', mockToken)
        localStorage.setItem('userInfo', JSON.stringify(mockUserInfo))
        
        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: '用户名或密码错误' }
      }
    } catch (error) {
      return { success: false, message: '登录失败，请重试' }
    }
  }

  // 登出方法
  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo && token.value) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    userInfo,
    token,
    isAuthenticated,
    login,
    logout,
    initUserInfo
  }
})
